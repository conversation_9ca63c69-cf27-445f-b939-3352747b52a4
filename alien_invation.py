import sys
import pygame
from ship import Ship

class Settings():
      """A class to store all settings for Alien Invasion."""
      def __init__(self):
            self.screen_width = 1200
            self.screen_height = 800
            self.bg_color = (230,230,230)

def run_game():
      pygame.init()
      ai_settings = Settings()
      screen = pygame.display.set_mode((ai_settings.screen_width,ai_settings.screen_height))
      pygame.display.set_caption("Alien Invasion")
      bg_color = ai_settings.bg_color
      ship = Ship(screen)

      while True:
            for event in pygame.event.get():
                  if event.type == pygame.QUIT:
                        screen.fill(bg_color)
                        ship.blitme()
                        sys.exit()
            pygame.display.flip()

run_game()